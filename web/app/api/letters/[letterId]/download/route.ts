export const dynamic = "force-dynamic";

import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@/lib/supabase-server';
import { captureApiError } from '@/utils/errorMonitoring';
import puppeteer from 'puppeteer';
import { getTemplateById } from '@/utils/letter-templates/applicationLetterTemplates';

// Maximum duration for the API route
export const maxDuration = 30; // 30 seconds

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ letterId: string }> }
) {
  let letterId: string | undefined;
  
  try {
    const resolvedParams = await params;
    letterId = resolvedParams.letterId;
    
    if (!letterId) {
      return NextResponse.json({
        error: 'Letter ID is required'
      }, { status: 400 });
    }

    // Get access token from request header
    const accessToken = request.headers.get('x-supabase-auth');

    if (!accessToken) {
      return NextResponse.json({
        error: 'Authentication required'
      }, { status: 401 });
    }

    // Create Supabase client
    const supabase = await createClient();
        
    // Get user with the provided token
    const { data: { user }, error: userError } = await supabase.auth.getUser(accessToken);

    if (userError || !user) {
      console.error('Error getting user with token:', userError);
      return NextResponse.json({
        error: 'Invalid authentication token'
      }, { status: 401 });
    }

    // Get the letter from database
    const { data: letter, error: fetchError } = await supabase
      .from('letters')
      .select('id, user_id, plain_text, design_html, template_id, created_at')
      .eq('id', letterId)
      .eq('user_id', user.id) // Ensure user can only access their own letters
      .single();

    if (fetchError || !letter) {
      console.error('Error fetching letter:', fetchError);
      return NextResponse.json({
        error: 'Letter not found or access denied'
      }, { status: 404 });
    }

    // ---------- Launch browser depending on environment ----------
    let browser;
    let page;

    const isServerless = !!process.env.AWS_REGION || !!process.env.NETLIFY || !!process.env.VERCEL;
    const isWindowsLocal = process.platform === 'win32' && !isServerless;

    if (true) {
      // Dynamically import full puppeteer to avoid increasing bundle size for prod.
      const puppeteer = (await import('puppeteer')).default;
      browser = await puppeteer.launch({
        args: ['--no-sandbox', '--disable-setuid-sandbox', '--disable-dev-shm-usage'],
        headless: true,
      });

      // Create a new page
      page = await browser.newPage();

      // Wait for all fonts to be loaded
      await page.evaluateHandle('document.fonts.ready');
    } else {
      // Serverless / Linux builds – keep lightweight setup.
      const puppeteerCore = (await import('puppeteer-core')).default;
      const chromium = (await import('@sparticuz/chromium-min')).default;

      browser = await puppeteerCore.launch({
        args: chromium.args,
        headless: 'shell',
        executablePath: await chromium.executablePath("https://github.com/Sparticuz/chromium/releases/download/v137.0.1/chromium-v137.0.1-pack.x64.tar"),
      });

      // Create a new page
      page = await browser.newPage();

      // Wait for all fonts to be loaded
      await page.evaluateHandle('document.fonts.ready');
    }

    try {
      // Set content to the page
      await page.setContent(letter.design_html, {
        waitUntil: 'networkidle0', // Wait until network is idle
      });

      // Set page size to A4
      await page.setViewport({
        width: 794, // A4 width in pixels (72 dpi)
        height: 1123, // A4 height in pixels (72 dpi)
        deviceScaleFactor: 2, // Higher scale for better quality
      });

      // Generate PDF
      const pdf = await page.pdf({
        format: 'A4',
        printBackground: true,
        margin: {
          top: '0',
          right: '0',
          bottom: '0',
          left: '0',
        },
      });

      // Close browser
      await browser.close();

      const selectedTemplate = letter.template_id ? getTemplateById(letter.template_id) : undefined;

      // Return PDF as response
      return new NextResponse(pdf, {
        status: 200,
        headers: {
          'Content-Type': 'application/pdf',
          'Content-Disposition': `attachment; filename="Surat_Lamaran_Gigsta_${selectedTemplate?.name}.pdf"`,
          'Cache-Control': 'no-cache, no-store, must-revalidate',
        },
      });
    } catch (pdfError) {
      console.error('PDF generation error:', pdfError);
      captureApiError('pdf-generation', pdfError as Error, {
        letterId: letterId
      });
      return NextResponse.json({
        error: 'Failed to generate PDF. Please try downloading as HTML or text instead.'
      }, { status: 500 });
    }
    
  } catch (error) {
    console.error('Error downloading letter:', error);
    
    // Capture error details with Rollbar
    captureApiError('download-letter', error, {
      requestUrl: request.url,
      letterId: letterId
    });
    
    return NextResponse.json({
      success: false,
      error: 'Failed to download letter. Please try again.'
    }, { status: 500 });
  }
}